#!/bin/bash
#export ENV=dev

set -a
set -o allexport; 
if [ ! -z "$MONGO_CRED" ]; then
    echo "setting creds"
    #source <(set +x && echo "$MONGO_CRED" | jq -r 'keys[] as $k | "\($k)=\(.[$k])"' | true); 
    for ITER in $(echo $MONGO_CRED | jq -r 'keys[] as $k | "\($k)=\(.[$k])"'); do export $ITER; done
else 
    echo "NOT setting creds"
fi
set +o allexport
set +a

env

mkdir -p /home/<USER>/logs/cacache_tmp 

# old script, but new should be compatible
cd /home/<USER>/ && npm run start:prod
#cd /home/<USER>/ && /usr/bin/pm2 start /home/<USER>/start.json 
