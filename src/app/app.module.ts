import { Module } from '@nestjs/common';
import { LoggerModule } from '../logger/logger.module';
import { LocalCacheModule } from '../localCache/localCache.module';
import { InfoModule } from '../components/info/info.module';
import { ScheduleModule } from '@nestjs/schedule';
import { NotFoundController } from '../components/blacklist/notFound.controller';
import { AdModule } from '../components/ad/ad.module';
import { HttpModule } from '../http/http.module';
import { HerringModule } from '../components/herring/herring.module';
import { K8sHealthModule } from '../components/k8s-health/k8s-health.module';
import { ConfigModule } from '../components/config/config.module';

@Module({
  imports: [
    LocalCacheModule,
    InfoModule,
    ScheduleModule.forRoot(),
    LoggerModule,
    AdModule,
    HerringModule,
    HttpModule,
    K8sHealthModule,
    ConfigModule,
  ],
  controllers: [NotFoundController]
})
export class SlaveModule {}

@Module({
  imports: [InfoModule],
  controllers: [NotFoundController]
})
export class MasterModule {}
