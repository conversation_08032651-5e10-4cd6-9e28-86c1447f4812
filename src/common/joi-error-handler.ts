import {
  BadRequestException,
  HttpException,
  UnprocessableEntityException
} from '@nestjs/common';
import type Joi<PERSON> from 'joi';
import { logger } from '../logger';

export function handleJoiError(error: JoiNS.ValidationError): HttpException {
  const details = error.details ?? [];
  const errDetails = details.map((d) => ({ message: d.message, path: d.path }));
  logger.error('VALIDATION', { errDetails });

  const hasRequired = details.some((d) => d.type === 'any.required');
  const hasInvalid = details.some((d) => d.type !== 'any.required');

  if (!hasRequired) {
    return new UnprocessableEntityException({
      message: 'Invalid parameter value',
      details: errDetails
    });
  }

  const message = hasInvalid ? 'Validation failed' : 'Missing required parameter';

  return new BadRequestException({
    message,
    details: errDetails
  });
}
