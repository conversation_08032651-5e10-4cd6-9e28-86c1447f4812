import { Injectable } from '@nestjs/common';
import { env } from '../../env/envalidConfig';
import { LoggerService } from '../../logger/logger.service';
import {
  DecodedId5AndIdentity,
  HerringID,
  HerringRandomID,
  Id5CorrectFormat,
  Id5ParamValue,
  IDGenerationEnum,
  IDGenerationEvent,
  KeyCd,
  KeyId5UIDCs
} from '../../types';
import { URLParamsHelper } from '../../utils';
import { IdHelperService } from '../servicesWIthoutModule/idHelper.service';
import { IAdSchema } from './ad.schema';
import { ID5DecoderService } from './id5decoder.service';

export enum AdValidationErrorEnum {
  IN_PARAMS_AOUSERID_EXISTS = 'IN_PARAMS_AOUSERID_EXISTS',
  ID5_IS_0 = 'ID5_IS_0',
  ID5_NOT_PROVIDED = 'ID5_NOT_PROVIDED',
  NO_CONSENT = 'NO_CONSENT',
  ID5_INVALID_FORMAT = 'ID5_INVALID_FORMAT'
}

@Injectable()
export class AdService {
  constructor(
    private readonly id5DecoderService: ID5DecoderService,
    private readonly idHelperService: IdHelperService,
    private readonly logger: LoggerService
  ) {
    this.logger.setContext(AdService.name);
  }
  /**
   * Generates redirect URL based on request parameters
   * Validates parameters and decodes ID5 if valid
   * @param requestParams - The request parameters
   * @returns string - The generated redirect URL
   */
  async getRedirectUrl(requestParams: IAdSchema): Promise<string> {
    const startTime = Date.now();
    this.logger.log('GET_REDIRECT_URL_START', { startTime, requestParams });

    const { id5, gdpr: consent }: { id5: Id5ParamValue; gdpr: string } = requestParams;
    const baseUrl: string = this.prepareRedirectUrl(env.AD_SERVER_URL, requestParams);
    const { validId5, validationErrors } = this.validateParams(id5, consent, requestParams);

    if (this.hasValidationErrors(validationErrors)) {
      return this.getRedirectUrlForInvalidParams(validationErrors, baseUrl);
    }

    if (!validId5) {
      this.logger.log(
        'GET_FOR_AD_REQUEST_END',
        this.generateLogEvent(startTime, requestParams)
      );
      return baseUrl;
    }
    const decodedIds: DecodedId5AndIdentity | null =
      await this.id5DecoderService.decodeId5AndGetIdentity(validId5);

    if (decodedIds) {
      this.logger.log(
        'GET_FOR_AD_REQUEST_END',
        this.generateLogEvent(startTime, requestParams, decodedIds)
      );
      return this.prepareRedirectUrlWithHerringIdAndSpratId(baseUrl, decodedIds);
    }

    this.logger.log('GET_REDIRECT_URL_END', this.generateLogEvent(startTime, requestParams));

    return this.prepareRedirectUrlWithRandomSpratIds(baseUrl);
  }

  createRandomIds(): HerringRandomID {
    return {
      spratId: `sr${this.idHelperService.generateNewRandomId().padStart(20, '0')}`,
      herringId: `hr${this.idHelperService.generateNewRandomId().padStart(20, '0')}`
    };
  }

  async getOrCreateConsistentIds(decodedIds: DecodedId5AndIdentity): Promise<HerringID> {
    const { id5Uid, identityId5 } = decodedIds;

    const herringCdKey: KeyCd = identityId5 ? `ind:${identityId5}` : `i5uid:${id5Uid}`;
    const herringCd = await this.idHelperService.getOrCreateHerringID(
      herringCdKey,
      decodedIds
    );

    const herringCsKey: KeyId5UIDCs = `id5uid:cs:${id5Uid}`;
    const herringCs = await this.idHelperService.getOrCreateHerringID(
      herringCsKey,
      decodedIds
    );

    return {
      spratId: `sp${herringCs.padStart(20, '0')}`,
      herringId: `he${herringCd.padStart(20, '0')}`
    };
  }

  private validateParams(
    id5: Id5ParamValue | undefined,
    consent: string,
    requestParams: IAdSchema
  ): { validId5: Id5CorrectFormat | undefined; validationErrors: AdValidationErrorEnum[] } {
    const validationErrors: AdValidationErrorEnum[] = [];
    let validId5 = this.validateId5(id5, validationErrors);

    // validate aouserid param
    if ('aouserid' in requestParams) {
      this.logger.warn('FIELD_AOUSERID_EXISTS');
      validationErrors.push(AdValidationErrorEnum.IN_PARAMS_AOUSERID_EXISTS);
      validId5 = undefined;
    }

    // validate consent param
    if (consent !== '1') {
      this.logger.warn('NO_CONSENT', { consent });
      validationErrors.push(AdValidationErrorEnum.NO_CONSENT);
      validId5 = undefined;
    }

    return { validId5, validationErrors };
  }

  private validateId5(
    id5: Id5ParamValue | undefined,
    errors: AdValidationErrorEnum[]
  ): Id5CorrectFormat | undefined {
    if (id5 === undefined) {
      this.logger.warn('ID5_NOT_PROVIDED');
      errors.push(AdValidationErrorEnum.ID5_NOT_PROVIDED);
      return undefined;
    }
    if (id5 === '0') {
      this.logger.warn('ID5_IS_0', { id5 });
      errors.push(AdValidationErrorEnum.ID5_IS_0);
      return undefined;
    }
    if (typeof id5 === 'string') {
      const isValidFormat = id5.startsWith('ID5*');

      if (!isValidFormat) {
        this.logger.warn('ID5_INVALID_FORMAT', { id5 });
        errors.push(AdValidationErrorEnum.ID5_INVALID_FORMAT);
        return undefined;
      }
      return id5 as Id5CorrectFormat;
    }
    return undefined;
  }

  private getRedirectUrlForInvalidParams(
    errors: AdValidationErrorEnum[],
    baseUrl: string
  ): string {
    const firstError = errors[0];
    switch (firstError) {
      case AdValidationErrorEnum.ID5_NOT_PROVIDED:
        return this.prepareRedirectUrlWithRandomSpratIds(baseUrl);
      default:
        return baseUrl;
    }
  }

  private async prepareRedirectUrlWithHerringIdAndSpratId(
    baseUrl: string,
    decodedIds: DecodedId5AndIdentity
  ): Promise<string> {
    const { spratId: aouserid, herringId: ppid } =
      await this.getOrCreateConsistentIds(decodedIds);
    return this.prepareRedirectUrl(baseUrl, { aouserid, ppid });
  }

  private prepareRedirectUrlWithRandomSpratIds(baseUrl: string): string {
    const { spratId: aouserid, spratId: ppid } = this.createRandomIds();
    this.logger.log('GET_REDIRECT_URL_START', {
      desc: 'generated random sprat IDs',
      aouserid,
      ppid
    });

    return this.prepareRedirectUrl(baseUrl, { aouserid, ppid });
  }

  private prepareRedirectUrl(
    baseUrl: string,
    query: Record<string, string | number | boolean>
  ): string {
    const url = new URLParamsHelper(baseUrl, '/');
    for (const [key, val] of Object.entries(query)) {
      url.add(key, val); // add new, if already exists, it will be overwritten
    }
    return url.toString();
  }

  private hasValidationErrors(validationErrors: AdValidationErrorEnum[]): boolean {
    return Object.keys(validationErrors).length > 0;
  }

  private generateLogEvent(
    startTime: number,
    requestParams: IAdSchema,
    decodedIds?: DecodedId5AndIdentity
  ): IDGenerationEvent {
    const { id5Uid, identityId5 } = decodedIds ?? {};

    return {
      createdAt: startTime,
      type: IDGenerationEnum.AD_REQUEST,
      modelInputs: {
        id5Uid,
        identityId5
      },
      timeTakenMs: Date.now() - startTime,
      spratId: undefined,
      herringId: undefined,
      requestParams,
      probXdIds: decodedIds ? { uid: id5Uid, id5Individual: identityId5 } : {}
    };
  }
}
