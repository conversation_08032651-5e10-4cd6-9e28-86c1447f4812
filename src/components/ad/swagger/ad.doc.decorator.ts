import { applyDecorators } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiResponse } from '@nestjs/swagger';
import { AdRedirectResponseModel } from './ad.models';
import { logger } from '../../../logger';

/**
 * Composite Swagger decorator for the GET /ad.xml endpoint
 * Combines all API documentation decorators into a single decorator
 */
export function GetAdXmlDoc(): ReturnType<typeof applyDecorators> {
  logger.log('Swagger docs available at: /doc');

  return applyDecorators(
    ApiOperation({
      summary: 'Ad request with ID enrichment and redirect',
      description: `
**Purpose**: This endpoint processes ad requests by enriching them with user identification parameters and redirecting to the ad server.

**ID Enrichment Process**:
1. Receives ID5 identifier and GDPR consent flag
2. If consent is granted (gdpr=1) and ID5 is valid:
   - Decodes the ID5 identifier
   - Generates or retrieves consistent herring ID (ppid) and sprat ID (aouserid)
3. If consent is not granted or ID5 is invalid/missing:
   - Generates random IDs or passes through without modification
4. Redirects to the ad server with all original parameters plus enriched IDs

**Example Requests**:

*Happy path with valid ID5:*
\`\`\`
GET /ad.xml/?id5=ID5*AgAAAAVz.../gdpr=1/a=A/b=B/c=C...
\`\`\`

*Without ID5 (random IDs generated):*
\`\`\`
GET /ad.xml/?gdpr=1/a=A/b=B/c=C...
\`\`\`

*Without consent (passthrough):*
\`\`\`
GET /ad.xml/?id5=ID5*AgAAAAVz.../gdpr=0/a=A/b=B/c=C...
\`\`\`

**Note**: This endpoint uses "/" as the query parameter delimiter (not "&").

**Response**: Returns a 307 Temporary Redirect to the ad server (default: https://tvn.adocean.pl/ad.xml/) with enriched parameters.

**Important**: Swagger UI will automatically follow the redirect, so you will see the response from the ad server, which is 200 OK. To see the response from this endpoint, you need to open the request in a new tab or use a curl command. For example: \`curl -v 'localhost:4001/ad.xml/?a=A/b=B/c=C'\`

Or change in postman settings to not follow redirects. Settings Icon -> Settings -> Headers -> Automatically follow redirects.
      `
    }),
    ApiQuery({
      name: 'id5',
      required: false,
      type: String,
      description:
        'ID5 identifier in format ID5*{string} or "0". Used for user identification and ID enrichment.',
      example: 'ID5*AgAAAAVzTrKM...'
    }),
    ApiQuery({
      name: 'gdpr',
      required: false,
      type: String,
      description: 'GDPR consent flag. Must be "1" for ID processing to occur.',
      example: '1'
    }),
    ApiQuery({
      name: 'isDebug',
      required: false,
      type: Boolean,
      description:
        'Enable debug mode for detailed logging. Related header x-tvn-debug-400 will be set if validation errors are present.',
      example: false
    }),
    ApiQuery({
      name: 'Additional Parameters',
      required: false,
      description:
        'All other query parameters are accepted and passed through to the ad server. ' +
        'Common parameters include: aocodetype, id, uniq, traffic, category, hostname, terminal, device, w, h, vis, gdpr_npa, floating, duration, gdpr_tctvn, etc.',
      example:
        'aocodetype=1/id=ABC/uniq=XYZ/traffic=fashion/category=seriale/hostname=party.pl/device=desktop/w=408'
    }),
    ApiResponse({
      status: 307,
      description:
        'Temporary Redirect - Successfully processed request and redirecting to ad server with enriched parameters. ' +
        'The Location header contains the redirect URL with added/modified parameters: aouserid (sprat ID) and ppid (herring ID).',
      type: AdRedirectResponseModel,
      headers: {
        Location: {
          description: 'URL to redirect to (ad server with enriched parameters)',
          schema: {
            type: 'string',
            example:
              'https://tvn.adocean.pl/ad.xml/?aocodetype=1/id=ABC123/aouserid=sp00000123456789012345/ppid=he00000987654321098765/...'
          }
        }
      }
    }),
    ApiResponse({
      status: 500,
      description: 'Internal Server Error - An error occurred while processing the request'
    })
  );
}
