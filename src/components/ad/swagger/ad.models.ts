import { ApiProperty } from '@nestjs/swagger';

/**
 * Query parameters model for the ad.xml endpoint
 * Note: This endpoint accepts many query parameters (all are passed through to the ad server),
 * but only these three are explicitly validated by the service.
 */
export class AdQueryParamsModel {
  @ApiProperty({
    description:
      'ID5 identifier in format ID5*{string} or "0". Used for user identification and ID enrichment. ' +
      'If valid and consent is granted, the service will decode it and generate/retrieve herring and sprat IDs.',
    example: 'ID5*AgAAAAVz...truncated',
    required: false,
    type: String
  })
  id5?: string;

  @ApiProperty({
    description:
      'GDPR consent flag. Must be "1" for ID processing to occur. ' +
      'If not "1", the request is passed through without ID enrichment.',
    example: '1',
    required: false,
    type: String
  })
  gdpr?: string;

  @ApiProperty({
    description:
      'Enable debug mode for detailed logging and debug information in response. ' +
      'When enabled, additional debugging information may be included in the response.',
    example: true,
    required: false,
    type: <PERSON>olean
  })
  isDebug?: boolean;

  // Note: All other query parameters are accepted and passed through to the ad server.
  // Common parameters include: aocodetype, id, uniq, traffic, category, hostname, terminal,
  // device, w, h, vis, gdpr_npa, floating, duration, gdpr_tctvn, etc.
  [key: string]: any;
}

/**
 * Response model for the ad.xml endpoint redirect
 * This endpoint returns a 307 Temporary Redirect with a URL to the ad server
 */
export class AdRedirectResponseModel {
  @ApiProperty({
    description:
      'The redirect URL to the ad server with enriched parameters. ' +
      'This URL will include the original query parameters plus added/modified parameters: ' +
      'aouserid (sprat ID) and ppid (herring ID) generated from ID5 decoding, or random IDs if ID5 processing fails/is skipped.',
    example:
      'https://tvn.adocean.pl/ad.xml/?id=random_string/aouserid=sp00000123456789012345/ppid=he00000987654321098765...',
    type: String
  })
  url!: string;
}
