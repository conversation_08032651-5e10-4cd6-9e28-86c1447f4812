import {
  Controller,
  Delete,
  Get,
  Head,
  HttpException,
  NotFoundException,
  Patch,
  Post,
  Put,
  Req
} from '@nestjs/common';

@Controller('/*')
export class NotFoundController {
  @Get()
  @Post()
  @Put()
  @Delete()
  @Patch()
  @Head()
  handleNotFound(@Req() req: Request): HttpException {
    throw new NotFoundException({
      message: `No route found for ${req.method} ${req.url}`
    });
  }
}
