import { PipeTransform, Injectable } from '@nestjs/common';
import { getSchemaForVersion } from './herring.schema';
import { IHerringAPIGetParams, VersionEnum } from '../../types';
import Joi from 'joi';
import { handleJoiError } from '../../common/joi-error-handler';
import { LoggerService } from '../../logger/logger.service';

@Injectable()
export class HerringVersionAwarePipe implements PipeTransform<any, IHerringAPIGetParams> {
  constructor(private readonly logger: LoggerService) {
    this.logger.setContext(HerringVersionAwarePipe.name);
  }

  transform(value: unknown): IHerringAPIGetParams {
    const versionSchema = Joi.object<{ version: VersionEnum }>({
      version: Joi.string()
        .default(VersionEnum.v_1_0_0)
        .valid(...Object.values(VersionEnum))
    });

    const { error: versionError, value: versionValue } = versionSchema.validate(value, {
      allowUnknown: true
    });

    if (versionError) {
      throw handleJoiError(versionError);
    }

    const schema = getSchemaForVersion(versionValue.version);

    const { error, value: validatedValue } = schema.validate(value, {
      abortEarly: false,
      convert: true,
      stripUnknown: false,
      allowUnknown: true
    });

    if (error) {
      throw handleJoiError(error);
    }

    const schemaKeys = Object.keys(schema.describe().keys);
    const inputKeys = Object.keys(value as object);
    const unknownParams = inputKeys.filter((key) => !schemaKeys.includes(key));

    if (unknownParams.length > 0) {
      const unknowns = Object.fromEntries(
        unknownParams.map((key) => [key, (value as any)[key]])
      );
      this.logger.warn('UNKNOWN_QUERY_PARAMS', { params: unknowns });
    }

    return validatedValue;
  }
}
