import { Injectable } from '@nestjs/common';
import { LoggerService } from '../../logger/logger.service';
import { RedisService } from '../../redis/redis.service';
import { ID5DecoderService } from '../ad/id5decoder.service';
import {
  IDGenerationEnum,
  ProbXdIds,
  IDLogEvent,
  KeyPcd,
  KeyPcdId,
  KeySidCd,
  KeyIdentity,
  KeyBpidtCd,
  KeyId5UID,
  KeyTpidSidCd,
  KeyTaidSidCd,
  KeyTpidCd,
  KeyTaidCd,
  KeySidCs,
  KeyId5UIDCs,
  KeyTaidCs,
  KeyTpidCs,
  KeyBpidtCs,
  IDGenerationEvent,
  DecodedId5AndIdentity,
  Id5ParamValue
} from '../../types';
import { createHash } from 'crypto';
import { IdHelperService } from '../servicesWIthoutModule/idHelper.service';
import { LogInputs } from '../../types/logger.type';

export interface HerringIDWithCsidCdid {
  csid: string;
  cdid: string;
}

type GetHerringIdInputs = {
  taid: string | undefined;
  tpid: string | undefined;
  sid: string | undefined;
  id5Uid: string | undefined;
  id5Individual: string | undefined;
  boltProfileIdToken: string | undefined;
  wakeId: string | undefined;
  pcdId: string | undefined;
};

type GetSpratIdInputs = {
  ua: string | undefined;
  taid: string | undefined;
  tpid: string | undefined;
  sid: string | undefined;
  id5Uid: string | undefined;
  boltProfileIdToken: string | undefined;
};

type LogIdGenerationEventInputs = {
  ua: string;
  taid: string | undefined;
  tpid: string | undefined;
  sid: string | undefined;
  id5: string | undefined;
  boltProfileIdToken: string | undefined;
  wakeId: string | undefined;
  pcdId: string | undefined;
  decodedIds: DecodedId5AndIdentity | null;
  startTime: number;
};

@Injectable()
export class HerringService {
  constructor(
    private readonly redisService: RedisService,
    private readonly id5DecoderService: ID5DecoderService,
    private readonly idHelperService: IdHelperService,
    private readonly logger: LoggerService
  ) {
    this.logger.setContext(HerringService.name);
  }

  private useragentHash(useragent: string | undefined): string {
    return createHash('md5')
      .update(useragent ?? '', 'utf8')
      .digest('hex')
      .substring(0, 6);
  }

  private async getProbCrossDeviceId(wakeId: string | undefined): Promise<string | undefined> {
    if (!wakeId) {
      return;
    }

    const key: KeyPcd = `pcd:${wakeId}`;
    const pcdId = await this.redisService.get<string>(key);
    if (!pcdId) {
      return;
    }

    this.logger.log('GET_PROB_CROSS_DEVICE_ID', { wakeId, pcdId });
    return pcdId;
  }

  private isChrome(ua: string | undefined): boolean {
    return !!ua && ua.includes('Chrome') && !ua.includes('Edg') && !ua.includes('OPR');
  }

  private async getSpratId(args: GetSpratIdInputs): Promise<string> {
    const { ua, taid, tpid, sid, id5Uid, boltProfileIdToken } = args;

    const isChrome = this.isChrome(ua);
    let herringCs: string;

    if (isChrome && sid) {
      const keySidCs: KeySidCs = `sid:cs:${sid}`;
      const inputs: LogInputs = { isChrome, sid };

      herringCs = await this.idHelperService.getOrCreateHerringID(keySidCs, inputs);
    } else if (id5Uid) {
      const keyId5UidCs: KeyId5UIDCs = `id5uid:cs:${id5Uid}`;
      const inputs: LogInputs = { id5Uid };

      herringCs = await this.idHelperService.getOrCreateHerringID(keyId5UidCs, inputs);
    } else if (tpid) {
      const uaHash = this.useragentHash(ua);
      const keyTpidCs: KeyTpidCs = `tpid:cs:${tpid}:${uaHash}`;
      const inputs: LogInputs = { tpid, uaHash };

      herringCs = await this.idHelperService.getOrCreateHerringID(keyTpidCs, inputs);
    } else if (taid) {
      const uaHash = this.useragentHash(ua);
      const keyTaidCs: KeyTaidCs = `taid:cs:${taid}:${uaHash}`;
      const inputs: LogInputs = { taid, uaHash };

      herringCs = await this.idHelperService.getOrCreateHerringID(keyTaidCs, inputs);
    } else if (boltProfileIdToken) {
      const uaHash = this.useragentHash(ua);
      const csKey: KeyBpidtCs = `bpidt:cs:${boltProfileIdToken}:${uaHash}`;

      const inputs: LogInputs = { boltProfileIdToken, uaHash };

      herringCs = await this.idHelperService.getOrCreateHerringID(csKey, inputs);
    } else {
      herringCs = this.idHelperService.generateNewRandomId();
    }

    this.logger.log('GET_SPRAT_ID', { herringCs });

    return herringCs;
  }

  private async getHerringIdWithSid(sid: string): Promise<string | undefined> {
    const keyTpidSidCd: KeyTpidSidCd = `tpid;sid:cd:${sid}`;
    let herringCd = (await this.redisService.get<string>(keyTpidSidCd)) ?? undefined;

    const logEntry: IDLogEvent = {
      inputs: { sid },
      db_key: keyTpidSidCd,
      db_value: herringCd,
      is_generated: false
    };
    this.logger.log('GET_HERRING_ID_WITH_SID', logEntry);

    if (!herringCd) {
      const keyTaidSidCd: KeyTaidSidCd = `taid;sid:cd:${sid}`;
      herringCd = (await this.redisService.get<string>(keyTaidSidCd)) ?? undefined;

      const logEntry: IDLogEvent = {
        inputs: { sid },
        db_key: keyTaidSidCd,
        db_value: herringCd,
        is_generated: false
      };
      this.logger.log('GET_HERRING_ID_WITH_SID', logEntry);
    }

    return herringCd;
  }

  private async getHerringId(args: GetHerringIdInputs): Promise<string> {
    const { taid, tpid, sid, id5Uid, id5Individual, boltProfileIdToken, wakeId, pcdId } = args;

    let herringCd: string;

    if (taid) {
      let getterKey: KeyTpidCd | KeyTaidCd;
      let getterValue: string;
      if (tpid && tpid !== taid) {
        getterKey = `tpid:cd:${tpid}`;
        getterValue = tpid;
      } else {
        getterKey = `taid:cd:${taid}`;
        getterValue = taid;
      }
      const inputs: LogInputs = {
        [getterKey]: getterValue
      };

      herringCd = await this.idHelperService.getOrCreateHerringID(getterKey, inputs);

      // If sid is available, save for later reference
      if (sid) {
        const keyTaidSidCd: KeyTaidSidCd = `taid;sid:cd:${sid}`;
        await this.redisService.set(keyTaidSidCd, herringCd);
        if (tpid) {
          const keyTpidSidCd: KeyTpidSidCd = `tpid;sid:cd:${sid}`;
          await this.redisService.set(keyTpidSidCd, herringCd);
        }
      }
    } else if (sid) {
      // NO TAID NEITHER TPID
      const herringCdFromSid = await this.getHerringIdWithSid(sid);

      if (!herringCdFromSid) {
        if (pcdId) {
          const pcdKey: KeyPcdId = `pcd_id:${pcdId}`;
          herringCd = await this.idHelperService.getOrCreateHerringID(pcdKey, { pcdId });
          this.logger.log('USING_PROBABILISTIC_GRAPH', { wakeId, pcdId, herringCd });
        } else if (id5Individual) {
          const indKey: KeyIdentity = `ind:${id5Individual}`;
          herringCd = await this.idHelperService.getOrCreateHerringID(indKey, {
            id5Individual
          });
        } else {
          const sidKey: KeySidCd = `sid:cd:${sid}`;
          herringCd = await this.idHelperService.getOrCreateHerringID(sidKey, { sid });
        }
      } else {
        herringCd = herringCdFromSid;
      }
    } else if (boltProfileIdToken) {
      const cdKey: KeyBpidtCd = `bpidt:cd:${boltProfileIdToken}`;
      herringCd = await this.idHelperService.getOrCreateHerringID(cdKey, {
        boltProfileIdToken
      });
    } else if (pcdId) {
      const pcdKey: KeyPcdId = `pcd_id:${pcdId}`;
      herringCd = await this.idHelperService.getOrCreateHerringID(pcdKey, { pcdId });
      this.logger.log('USING_PROBABILISTIC_GRAPH', { wakeId, pcdId, herringCd });
    } else if (id5Individual) {
      const indKey: KeyIdentity = `ind:${id5Individual}`;
      herringCd = await this.idHelperService.getOrCreateHerringID(indKey, { id5Individual });
    } else if (id5Uid) {
      const i5uidKey: KeyId5UID = `i5uid:${id5Uid}`;
      herringCd = await this.idHelperService.getOrCreateHerringID(i5uidKey, { id5Uid });
    } else {
      herringCd = this.idHelperService.generateNewRandomId();
    }

    this.logger.log('GET_HERRING_ID', { herringCd });

    return herringCd;
  }

  async get(
    ua: string,
    taid: string | undefined,
    tpid: string | undefined,
    sid: string | undefined,
    id5: Id5ParamValue,
    boltProfileIdToken: string | undefined,
    wakeId: string | undefined
  ): Promise<HerringIDWithCsidCdid> {
    const startTime = Date.now();
    this.logger.log('ID_GENERATION_EVENT_START', { startTime });

    const decodedIds =
      id5 !== '0' ? await this.id5DecoderService.decodeId5AndGetIdentity(id5) : null;

    const id5Uid = decodedIds?.id5Uid;
    const id5Individual = decodedIds?.identityId5;
    const pcdId = await this.getProbCrossDeviceId(wakeId);

    this.logIdGenerationEvent({
      ua,
      taid,
      tpid,
      sid,
      id5,
      boltProfileIdToken,
      wakeId,
      pcdId,
      decodedIds,
      startTime
    });

    const spratId = await this.getSpratId({
      ua,
      taid,
      tpid,
      sid,
      id5Uid,
      boltProfileIdToken
    });

    const herringId = await this.getHerringId({
      taid,
      tpid,
      sid,
      id5Uid,
      id5Individual,
      boltProfileIdToken,
      wakeId,
      pcdId
    });

    const ids: HerringIDWithCsidCdid = {
      csid: spratId,
      cdid: herringId
    };

    this.logger.log('ID_GENERATION_EVENT_END', {
      spratId,
      herringId,
      timeTakenMs: Date.now() - startTime
    });

    return ids;
  }

  private logIdGenerationEvent(args: LogIdGenerationEventInputs): void {
    const {
      ua,
      taid,
      tpid,
      sid,
      id5,
      boltProfileIdToken,
      wakeId,
      pcdId,
      decodedIds,
      startTime
    } = args;

    const inputs: LogInputs = {
      ua,
      taid,
      tpid,
      sid,
      id5,
      boltProfileIdToken,
      wakeId,
      pcdId
    };

    const probXdIds: ProbXdIds = {};
    if (decodedIds) {
      probXdIds.id5 = { uid: decodedIds.id5Uid, id5Individual: decodedIds.identityId5 };
    }
    if (pcdId) {
      probXdIds['pcd:baseline'] = { pcdId };
    }

    const logEventObj: IDGenerationEvent = {
      createdAt: startTime,
      type: IDGenerationEnum.WAKE,
      modelInputs: inputs,
      probXdIds,
      timeTakenMs: 0
    };

    this.logger.log('ID_GENERATION_EVENT_START', { logEventObj });
  }
}
