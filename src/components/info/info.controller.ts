import { Controller, Get, HttpException, HttpStatus } from '@nestjs/common';
import { InfoService } from './info.service';
import { LoggerService } from '../../logger/logger.service';

@Controller('info')
export class InfoController {
  constructor(
    private readonly infoService: InfoService,
    private logger: LoggerService
  ) {}

  @Get('/app')
  appInfo() {
    try {
      return this.infoService.appInfo;
    } catch (e: unknown) {
      this.logger.error('UNKNOWN_SERVER_ERROR', { e });
      if (e instanceof HttpException) {
        throw e;
      }

      if (e instanceof Error) {
        throw new HttpException(e.message, 500);
      }

      throw new HttpException('UNKNOWN ERROR', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('/check')
  appCheck(): string {
    try {
      return this.infoService.healthCheck();
    } catch (e: unknown) {
      this.logger.error('UNKNOWN_SERVER_ERROR', { e });
      if (e instanceof HttpException) {
        throw e;
      }

      if (e instanceof Error) {
        throw new HttpException(e.message, HttpStatus.INTERNAL_SERVER_ERROR);
      }

      throw new HttpException('UNKNOWN ERROR', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
