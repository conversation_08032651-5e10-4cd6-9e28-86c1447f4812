import cluster from 'cluster';
import os from 'os';
import process from 'process';
import { env } from '../../env/envalidConfig';

const workerStartedTime = new Date();

export class InfoService {
  get appInfo() {
    return {
      maxCpu: this.cpuQuantity,
      version: this.appVersion,
      nodeVersion: this.nodeVersion,
      environment: this.environment,
      currentMachineTime: this.currentMachineTime,
      workerStartedTime
    };
  }

  get cpuQuantity(): number {
    return os.cpus().length;
  }

  get nodeVersion(): string {
    return process.version;
  }

  get appVersion(): string {
    return process.env.npm_package_version ?? 'NA';
  }

  get environment(): string | undefined {
    return env.APP_ENV;
  }

  get currentMachineTime(): Date {
    return new Date();
  }

  get workerStartedTime(): Date {
    return workerStartedTime;
  }

  healthCheck(): string {
    if (cluster.isWorker) {
      return 'OK';
    }

    const workers = cluster.workers;
    if (!workers || Object.keys(workers).length === 0) {
      throw new Error('HEALTH_CHECK_ERROR');
    }

    const workerStatuses = Object.values(workers).map((worker) => {
      if (worker) {
        return {
          cluster: worker.id?.toString() ?? 'unknown',
          state: (worker as any).state ?? 'unknown'
        };
      }
      return {
        cluster: 'unknown',
        state: 'unknown'
      };
    });

    return JSON.stringify(workerStatuses);
  }
}
