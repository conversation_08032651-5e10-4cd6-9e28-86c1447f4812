import { Injectable } from '@nestjs/common';
import { AsyncLocalStorage } from 'async_hooks';
import { returnAsArray } from '../utils/returnAsArray';

export interface RequestContext {
  requestId: string;
  isDebugMode: boolean;
  collectedLogs: string[];
}

@Injectable()
export class RequestContextService {
  private static readonly asyncLocalStorage = new AsyncLocalStorage<RequestContext>();

  /**
   * Initialize request context using AsyncLocalStorage.run() for automatic cleanup
   */
  static runWithContext<T>(context: RequestContext, callback: () => T): T {
    return this.asyncLocalStorage.run(context, callback);
  }

  static getContext(): RequestContext | undefined {
    return this.asyncLocalStorage.getStore();
  }

  static isDebugMode(): boolean {
    const context = this.getContext();
    return context?.isDebugMode ?? false;
  }

  static addValidationErrors(error: string | string[]): void {
    const context = this.getContext();
    if (context?.isDebugMode) {
      context.collectedLogs.push(...returnAsArray(error));
    }
  }

  static getValidationErrors(): string[] {
    const context = this.getContext();
    return context?.collectedLogs ?? [];
  }

  // for testing purposes only
  static clearValidationErrors(): void {
    const context = this.getContext();
    if (context) {
      context.collectedLogs = [];
    }
  }

  static getRequestId(): string | undefined {
    const context = this.getContext();
    return context?.requestId;
  }
}
