import { NestFastifyApplication } from '@nestjs/platform-fastify';
import { DocumentBuilder, SwaggerCustomOptions, SwaggerModule } from '@nestjs/swagger';
import { AdModule } from './components/ad/ad.module';
import { InfoModule } from './components/info/info.module';

/**
 * Initializes Swagger/OpenAPI documentation for the application
 * @param app - The NestJS Fastify application instance
 */
export function initSwagger(app: NestFastifyApplication): void {
  const config = new DocumentBuilder()
    .setTitle('School of Fish - Ad Serving API')
    .setDescription(
      'API for ID enrichment and ad serving. This service decodes ID5 identifiers, ' +
        'generates or retrieves herring and sprat IDs, and redirects to ad servers with enriched parameters.'
    )
    .setVersion('1.0')
    .addTag('Ad Serving', 'Endpoints for ad request processing and ID enrichment')
    .addTag('Info', 'Application information and health check endpoints')
    .build();

  const swaggerCustomOptions: SwaggerCustomOptions = {
    customCss: '.swagger-ui section.models { visibility: hidden;}'
  };

  const document = SwaggerModule.createDocument(app, config, {
    include: [AdModule, InfoModule]
  });

  SwaggerModule.setup('doc', app, document, swaggerCustomOptions);
}
