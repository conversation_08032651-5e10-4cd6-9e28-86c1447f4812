import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { FastifyRequest } from 'fastify';
import qs from 'qs';

/**
 * Interceptor that parses HTTP query parameters using a custom delimiter.
 *
 * This interceptor parses the query string from the request URL based on the specified delimiter.
 * The delimiter is defined once during instantiation and used consistently throughout requests.
 *
 * Enables support for custom query parameter formats in endpoints.
 */
@Injectable()
export class QueryDelimiterInterceptor implements NestInterceptor {
  constructor(private readonly delimiter: string | RegExp) {}
  /**
   * Intercepts the HTTP request and parses the query string using the specified delimiter.
   *
   * @param context The execution context of the request.
   * @param next The handler passing the request further.
   * @returns Observable with the response.
   */
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<FastifyRequest>();
    const queryString = request.url.split('?')[1];

    try {
      request.query = qs.parse(queryString, { delimiter: this.delimiter });
    } catch (error) {
      console.warn(`Failed to parse query string with delimiter: ${this.delimiter}`, error);
    }

    return next.handle();
  }
}
