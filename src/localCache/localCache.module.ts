import { Module } from '@nestjs/common';
import { CacheModule as NestJsCacheModule } from '@nestjs/cache-manager';
import { LocalCacheService } from './localCache.service';
import { env } from '../env/envalidConfig';
import { LoggerModule } from '../logger/logger.module';

const TTL = 10000;

@Module({
  imports: [
    NestJsCacheModule.register({
      isGlobal: true,
      ttl: TTL,
      store: 'memory',
      max: env.CACHE_MAX_SIZE
    }),
    LoggerModule
  ],
  providers: [LocalCacheService],
  exports: [LocalCacheService]
})
export class LocalCacheModule {}
