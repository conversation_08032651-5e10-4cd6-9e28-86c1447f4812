import { ConsoleLogger, Global, Injectable, LogLevel, Scope } from '@nestjs/common';
import { RequestContextService } from '../context/request-context.service';
import { env } from '../env/envalidConfig';
import { clc } from './cli-colors.util';

@Global()
@Injectable({ scope: Scope.TRANSIENT })
export class LoggerService extends ConsoleLogger {
  private allowedLogs: string[] = [];

  constructor(customManualOptions?: { setContext: string }) {
    super({
      prefix: env.APP_NAME,
      timestamp: true,
      json: env.APP_ENV !== 'local',
      compact: true
    });

    if (customManualOptions?.setContext) {
      this.setContext(customManualOptions.setContext);
    }
  }

  protected formatPid() {
    return '';
  }

  protected getTimestamp(): string {
    return new Intl.DateTimeFormat(undefined, {
      year: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      second: 'numeric',
      fractionalSecondDigits: 3,
      day: '2-digit',
      month: '2-digit'
    }).format(Date.now());
  }

  private getColorForLog(level: LogLevel) {
    switch (level) {
      case 'debug':
        return clc.magentaBright;
      case 'warn':
        return clc.yellow;
      case 'error':
        return clc.red;
      case 'verbose':
        return clc.cyanBright;
      case 'fatal':
        return clc.bold;
      default:
        return clc.green;
    }
  }

  colorize(message: string, logLevel: LogLevel) {
    const [msg, data] = message.split('|');

    const colorMsg = this.getColorForLog(logLevel);

    return `${colorMsg(msg)}${data ? `|${data}` : ''}`;
  }

  formatLogMsg(message: string, stringify: boolean, data?: object | string) {
    if (env.APP_ENV === 'local') {
      return `${message} ${data ? `| ${stringify ? JSON.stringify(data) : data}` : ''}`;
    }

    return { title: message, data };
  }

  isMessageAllowed(message: string): boolean {
    return this.allowedLogs.includes(message) || this.allowedLogs.length === 0;
  }

  private collectLog(message: string): void {
    const context = RequestContextService.getContext();
    if (context?.isDebugMode) {
      context.collectedLogs.push(message);
    }
  }

  log(message: string, data?: object | string, stringify = true) {
    if (this.isMessageAllowed(message)) {
      const new_message = `LOG_${message}`;
      super.log(this.formatLogMsg(new_message, stringify, data));
    }
  }

  verbose(message: string, data?: object | string, stringify = true) {
    if (this.isMessageAllowed(message)) {
      const new_message = `VERBOSE_${message}`;
      super.verbose(this.formatLogMsg(new_message, stringify, data));
    }
  }

  debug(message: string, data?: object | string, stringify = true) {
    if (env.DEBUG_MODE === 'ENABLED' && this.isMessageAllowed(message)) {
      const new_message = `DEBUG_${message}`;
      super.debug(this.formatLogMsg(new_message, stringify, data));
    }
  }

  warn(message: string, data?: object | string, stringify = true) {
    const new_message = `WARN_${message}`;
    this.collectLog(new_message);
    super.warn(this.formatLogMsg(new_message, stringify, data));
  }

  error(message: string, data?: object | string): void {
    const new_message = `ERROR_${message}`;
    this.collectLog(new_message);
    console.trace(new_message);
    super.error(this.formatLogMsg(new_message, true, data));
  }

  fatal(message: string, data?: object | string, stringify = true) {
    const new_message = `FATAL_${message}`;
    this.collectLog(new_message);
    console.trace(new_message);
    super.fatal(this.formatLogMsg(new_message, stringify, data));
  }
}
