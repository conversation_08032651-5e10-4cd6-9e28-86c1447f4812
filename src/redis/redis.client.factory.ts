import { FactoryProvider } from '@nestjs/common';
import Redis from 'ioredis';
import { env } from '../env/envalidConfig';
import { LoggerService } from '../logger/logger.service';
import { REDIS_TOKEN } from './redis.repository.interface';

export const RedisClientFactory: FactoryProvider<Redis> = {
  provide: REDIS_TOKEN,
  useFactory: () => {
    const logger = new LoggerService({ setContext: Redis.name });

    const redisInstance = new Redis({
      port: env.REDIS_PORT,
      host: env.REDIS_HOST,
      password: env.REDIS_PROVIDE_PASS ? env.REDIS_PASS : undefined,
      db: env.REDIS_DB_ID
    });

    redisInstance.on('connecting', () => {
      logger.verbose('CONNECTING');
    });

    redisInstance.on('connect', () => {
      logger.verbose('CONNECT');
    });

    redisInstance.on('ready', () => {
      logger.verbose('READY');
    });

    redisInstance.on('error', (e) => {
      logger.error('REDIS_CONNECTION', e);
      throw new Error(`Redis connection failed: ${e}`);
    });

    return redisInstance;
  },
  inject: []
};
