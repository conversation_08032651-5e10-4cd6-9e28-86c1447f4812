import { Controller, Get, NotFoundException, Query } from '@nestjs/common';
import { JoiParamPipe } from '../components/ad/joi-param.pipe';
import { keySchema } from './redis.schema';
import { RedisService } from './redis.service';

@Controller()
export class RedisController {
  constructor(private readonly redisService: RedisService) {}

  @Get('get_key')
  async getKey(
    @Query(new JoiParamPipe(keySchema)) params: { key: string }
  ): Promise<string | null> {
    const { key } = params;
    const value = await this.redisService.get<string>(key);
    if (!value) {
      throw new NotFoundException(`Key ${key} not found in Redis`);
    }
    return value;
  }
}
