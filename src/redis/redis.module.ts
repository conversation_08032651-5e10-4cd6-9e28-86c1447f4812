import { Module } from '@nestjs/common';
import { LoggerModule } from '../logger/logger.module';
import { RedisController } from '../redis/redis.controller';
import { RedisClientFactory } from './redis.client.factory';
import { RedisService } from './redis.service';
import { RedisRepository } from './repository/redis.repository';

@Module({
  imports: [LoggerModule],
  providers: [RedisClientFactory, RedisRepository, RedisService],
  controllers: [RedisController],
  exports: [RedisService]
})
export class RedisModule {}
