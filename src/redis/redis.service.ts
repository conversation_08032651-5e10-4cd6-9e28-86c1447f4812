import { Injectable } from '@nestjs/common';
import { LoggerService } from '../logger/logger.service';
import { RedisRepository } from './repository/redis.repository';

@Injectable()
export class RedisService {
  constructor(
    private logger: LoggerService,
    private readonly redisRepository: RedisRepository
  ) {
    this.logger.setContext(RedisService.name);
  }

  async get<T = unknown>(key: string): Promise<T | null> {
    const res = await this.redisRepository.get(key);

    if (res === null) {
      this.logger.log('REDIS_GET_NULL', { key });
      return null;
    }

    // if resulting string turns out to be a number, keep it as string or it will lose digits.
    if (!isNaN(Number(res))) {
      return res as T;
    }

    let payload: T | null = null;
    try {
      payload = JSON.parse(res ?? '');
    } catch (error) {
      this.logger.error('REDIS_GET_PARSE', { error, res });
      payload = res as T;
    }

    this.logger.log('REDIS_GET', { key, resLength: res?.length });
    return payload;
  }

  async set<T extends unknown>(key: string, value: T, ttlSeconds?: number): Promise<void> {
    const payload = typeof value === 'string' ? value : JSON.stringify(value);

    await this.redisRepository.set(key, payload);
    this.logger.log('REDIS_SET', { key, payloadLength: payload.length, ttlSeconds });
  }
}
