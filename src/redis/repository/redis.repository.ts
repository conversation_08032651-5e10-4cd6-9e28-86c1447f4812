import { Inject, Injectable, OnModuleD<PERSON>roy } from '@nestjs/common';
import { Redis } from 'ioredis';
import { REDIS_TOKEN, RedisRepositoryInterface } from '../redis.repository.interface';
import { returnAsArray } from '../../utils/returnAsArray';

@Injectable()
export class RedisRepository implements OnModuleDestroy, RedisRepositoryInterface {
  constructor(@Inject(REDIS_TOKEN) private readonly redisClient: Redis) {}

  onModuleDestroy(): void {
    this.redisClient.disconnect();
  }

  get(key: string): Promise<string | null> {
    return this.redisClient.get(key);
  }

  exists(key: string | string[]): Promise<number> {
    return this.redisClient.exists(returnAsArray(key));
  }

  async setWithExpiry(key: string, value: string, expiry: number): Promise<void> {
    await this.redisClient.set(key, value, 'EX', expiry);
  }

  async set(key: string, value: string): Promise<void> {
    await this.redisClient.set(key, value);
  }
}
