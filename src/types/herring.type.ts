import { LogInputs } from './logger.type';
import { IDGenerationEnum } from './model.enum';

export type DecodedId5AndIdentity = {
  id5Uid: string;
  identityId5: string;
};

export type ProbXdIds = {
  id5?: {
    uid: DecodedId5AndIdentity['id5Uid'];
    id5Individual: DecodedId5AndIdentity['identityId5'];
  };
  'pcd:baseline'?: { pcdId: string };
} & Record<string, any>;

export interface IDLogEvent {
  inputs?: LogInputs;
  db_key?: string;
  db_value?: string;
  is_generated?: boolean;
}

export interface IDGenerationEvent {
  createdAt: number; // timestamp in milliseconds
  type: IDGenerationEnum;
  requestParams?: any;
  modelInputs?: object;
  probXdIds?: ProbXdIds;
  spratId?: string;
  herringId?: string;
  timeTakenMs: number;
}

export interface HerringID {
  spratId: `sp${string}`;
  herringId: `he${string}`;
}

export interface HerringRandomID {
  spratId: `sr${string}`;
  herringId: `hr${string}`;
}

export type HerringIDType = HerringID | HerringRandomID;
