export type KeyIdentity = `ind:${string}`;
export type KeyId5UID = `i5uid:${string}`;
export type KeyId5UIDCs = `id5uid:cs:${string}`;
export type KeyID5 = `id5:${string}`;

export type KeySidCs = `sid:cs:${string}`;
export type KeyTaidCs = `taid:cs:${string}:${string}`;
export type KeyTpidCs = `tpid:cs:${string}:${string}`;
export type KeyBpidtCs = `bpidt:cs:${string}:${string}`;
export type KeyTaidCd = `taid:cd:${string}`;
export type KeyTpidCd = `tpid:cd:${string}`;
export type KeySidCd = `sid:cd:${string}`;
export type KeyBpidtCd = `bpidt:cd:${string}`;
export type KeyPcdId = `pcd_id:${string}`;
export type KeyTaidSidCd = `taid;sid:cd:${string}`; // ";" zamiast ":". Bład czy celowo?
export type KeyTpidSidCd = `tpid;sid:cd:${string}`; // ";" zamiast ":". Bład czy celowo?
export type KeyPcd = `pcd:${string}`;

export type KeyCd = KeyIdentity | KeyId5UID;

export type HerringKey =
  | KeyCd
  | KeyId5UIDCs
  | KeyID5
  | KeySidCs
  | KeyTaidCs
  | KeyTpidCs
  | KeyBpidtCs
  | KeyTaidSidCd
  | KeyTpidSidCd
  | KeyPcd
  | KeyTaidCd
  | KeyTpidCd
  | KeySidCd
  | KeyBpidtCd
  | KeyPcdId;
