import { RequiredValidatorSpec, Spec, makeValidator } from 'envalid';
import { positiveInteger } from './convertToNumeric';

/**
 * Helper function to convert a string to a number,
 * multiply it by a specified number and return it when environment variable is set.
 *
 * @param {number} multiplier - The number to multiply the input value by.
 * @param {Spec<number>} [specs] - The configuration options for the conversion.
 * @returns {RequiredValidatorSpec<number>} - The converted value based on the input value and multiplier.
 */
export const unitConverter = (
  multiplier: number,
  specs?: Spec<number>
): RequiredValidatorSpec<number> => {
  if (specs?.default) {
    specs.default *= multiplier;
  }
  if (specs?.devDefault) {
    specs.devDefault *= multiplier;
  }

  const validator = makeValidator((value: string): number => {
    const intValue = positiveInteger(value);

    return intValue * multiplier;
  });

  return validator(specs);
};

const partialHelper = (a: number) => {
  return (b: Spec<number>) => {
    return unitConverter(a, b);
  };
};

/**
 * Converts a string to a number, multiplies it by 1000 (milliseconds) and returns it when environment variable is set.
 */
export const seconds = partialHelper(1000);

/**
 * Converts a string to a number, multiplies it by 60_000 (milliseconds) and returns it when environment variable is set.
 */
export const minutes = partialHelper(1000 * 60);

/**
 * Converts a string to a number, multiplies it by 3_600_000 (milliseconds) and returns it when environment variable is set.
 */
export const hours = partialHelper(1000 * 60 * 60);

/**
 * Converts a string to a number, multiplies it by 86_400_000 (milliseconds) and returns it when environment variable is set.
 */
export const days = partialHelper(1000 * 60 * 60 * 24);

/**
 * Converts a string to a number, multiplies it by 1_048_576 (bytes) and returns it when environment variable is set.
 */
export const megaBytes = partialHelper(1024 * 1024);
