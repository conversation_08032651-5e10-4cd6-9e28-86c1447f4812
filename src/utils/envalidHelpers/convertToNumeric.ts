/**
 * Converts a string to a number.
 *
 * @param {string} value - The string to be converted.
 * @throws {Error} If the input string cannot be converted to a number.
 * @return {number} The converted numeric value.
 */
export const numeric = (value: string): number => {
  const numericValue = parseInt(value, 10);
  if (Number.isNaN(numericValue)) {
    throw new Error('Value must be a number.');
  }
  return numericValue;
};

/**
 * Validates if the input string is a positive integer and returns the corresponding number.
 *
 * @param {string} value - The input string to be validated.
 * @throws {Error} If the input string is not a positive integer.
 * @return {number} The positive integer value of the input string.
 */
export const positiveInteger = (value: string): number => {
  if (/\D/.test(value)) {
    throw new Error(`Input ${value} is Invalid.`);
  }

  return numeric(value);
};
