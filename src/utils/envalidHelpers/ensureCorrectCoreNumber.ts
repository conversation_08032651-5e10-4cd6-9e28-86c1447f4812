import * as os from 'os';
import { RequiredValidatorSpec, Spec, makeValidator } from 'envalid';
import { numeric, positiveInteger } from './convertToNumeric';

/**
 * Ensures that the input value is within the range of 0 and the number of CPUs available.
 *
 * @param {string} value - The input value to be validated.
 * @return {number} The validated number value.
 * @throws {Error} If the input value is less than 0 or greater than the number of CPUs.
 */
export const between0AndNumCPUs = makeValidator((value: string): number => {
  const numericValue = positiveInteger(value);
  if (numericValue > os.cpus().length - 1) {
    throw new Error(`number cannot be greater than number of CPUs: ${os.cpus().length}.`);
  }
  return numericValue;
});

/**
 * Body of the coreReducer function.
 * Reduces the core count based on the input value and the number of CPUs available.
 * If the input value is greater than the number of CPUs available, it sets the core count to 1.
 * If the input value is less than 0, it sets the core count to the number of CPUs available, plus the input value.
 * @param value - The input value to calculate the core count.
 * @return The calculated core count to subtract from the number of CPUs.
 */
export const _coreReducerBody = (value: string): number => {
  const numericValue = numeric(value);
  return Math.min(os.cpus().length - 1, numericValue);
};

/**
 * Ensures that the core count is within the range of the number of CPUs available.
 *
 * @param specs - The configuration options for the core count.
 * @returns The amount of cores to subtract from the number of CPUs available.
 */
export const coreReducer = (specs?: Spec<number>): RequiredValidatorSpec<number> => {
  if (specs?.default) {
    specs.default = Math.min(os.cpus().length - 1, specs.default);
  }
  if (specs?.devDefault) {
    specs.devDefault = Math.min(os.cpus().length - 1, specs.devDefault);
  }

  return makeValidator(_coreReducerBody)(specs);
};
