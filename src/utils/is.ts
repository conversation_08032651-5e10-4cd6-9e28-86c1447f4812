const regexes = {
  ipv4: /^(?:(?:\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.){3}(?:\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])$/,
  ipv6: /^((?=.*::)(?!.*::.+::)(::)?([\dA-F]{1,4}:(:|\b)|){5}|([\dA-F]{1,4}:){6})((([\dA-F]{1,4}((?!\3)::|:\b|$))|(?!\2\3)){2}|(((2[0-4]|1\d|[1-9])?\d|25[0-5])\.?\b){4})$/i
};

const existy = (value: unknown): boolean => value != null;
const isString = (value: unknown): value is string =>
  existy(value) && Object.prototype.toString.call(value) === '[object String]';
const isObject = (value: unknown): value is object =>
  Object.prototype.toString.call(value) === '[object Object]';
const isIp = (value: unknown): boolean =>
  isString(value) && (regexes.ipv4.test(value) || regexes.ipv6.test(value));

const is = {
  existy,
  ip: isIp,
  object: isObject,
  string: isString
};

export default is;
