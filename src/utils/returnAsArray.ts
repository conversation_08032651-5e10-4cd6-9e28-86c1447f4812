/**
 * Converts a given value into an array. If the value is already an array, it is returned as is.
 * If the value is not an array, the value wrapped in an array is returned.
 * This function is ment to be used when the value is null or undefined.
 *
 * @param value - The value to be converted into an array.
 * @return - The converted array value or an empty array.
 */
export const returnAsArray = <T>(value: T | T[]): T[] =>
  Array.isArray(value) ? value : [value];

/**
 * Converts a given value into an array. If the value is already an array, it is returned as is.
 * If the value is not an array, it checks if the value is not null or undefined and wraps it in an array,
 * otherwise it returns an empty array.
 *
 * @param value - The value to be converted into an array.
 * @return - The converted array value or an empty array.
 */
export const returnAsArrayEmpty = <T>(value: T | T[] | null | undefined): T[] =>
  Array.isArray(value) ? value : !!value ? [value] : [];
