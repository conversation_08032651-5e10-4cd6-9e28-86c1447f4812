/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
import { xml2json, json2xml, Options } from 'xml-js';
import { decode } from 'html-entities';

export const xmlParser = {
  fromXMLtoJSON: (
    xml: string | null | undefined,
    options?: Options.XML2JSON
  ): object | null => {
    if (!xml) return null;

    const json = xml2json(xml, { compact: true, ...options });

    return JSON.parse(json);
  },

  fromEncodedToJSON: (encoded: string, options?: Options.XML2JSON): object | null => {
    if (!encoded) return null;

    try {
      let xml = decodeURIComponent(encoded.replace(/\+/g, ' '));
      xml = xml.replace(/\r\n/g, ' ');
      const json = xml2json(xml, { compact: true, ...options });

      return JSON.parse(json);
    } catch (error) {
      return null;
    }
  },

  fromJSONtoXML: (json: any, decodeResponse = false, options?: Options.JS2XML): string => {
    if (!json) return '';

    json = JSON.stringify(json);
    const xml: string = json2xml(json, { compact: true, ...options });

    return decodeResponse ? decode(xml) : xml;
  },

  fromJSONtoEncoded: (json: any, options?: Options.JS2XML): string => {
    if (!json) return '';

    json = JSON.stringify(json);
    const xml: string = json2xml(json, { compact: true, ...options });

    return encodeURIComponent(xml);
  },

  fromEncodedToDecoded: (xml: string): string => {
    const decoded = decodeURIComponent(xml.replace(/\+/g, ' '));
    return decoded.replace(/\r\n/g, ' ');
  },

  fromDecodedToEncoded: (xml: string): string => encodeURIComponent(xml)
};
