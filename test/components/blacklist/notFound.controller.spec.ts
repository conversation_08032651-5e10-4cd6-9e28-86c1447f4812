import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { describe, it, expect, beforeEach } from 'vitest';
import { NotFoundController } from '../../../src/components/blacklist/notFound.controller';

describe('NotFoundController', () => {
  let controller: NotFoundController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [NotFoundController]
    }).compile();

    controller = module.get<NotFoundController>(NotFoundController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('handleNotFound', () => {
    it('should throw 404 NotFoundException for GET request', () => {
      const mockRequest = {
        method: 'GET',
        url: '/nonexistent/path'
      } as Request;

      expect(() => controller.handleNotFound(mockRequest)).toThrow(NotFoundException);
      expect(() => controller.handleNotFound(mockRequest)).toThrow(
        'No route found for GET /nonexistent/path'
      );
    });

    it('should throw 404 NotFoundException for POST request', () => {
      const mockRequest = {
        method: 'POST',
        url: '/api/unknown'
      } as Request;

      expect(() => controller.handleNotFound(mockRequest)).toThrow(NotFoundException);
      expect(() => controller.handleNotFound(mockRequest)).toThrow(
        'No route found for POST /api/unknown'
      );
    });

    it('should throw 404 NotFoundException for PUT request', () => {
      const mockRequest = {
        method: 'PUT',
        url: '/users/123'
      } as Request;

      expect(() => controller.handleNotFound(mockRequest)).toThrow(NotFoundException);
      expect(() => controller.handleNotFound(mockRequest)).toThrow(
        'No route found for PUT /users/123'
      );
    });

    it('should throw 404 NotFoundException for DELETE request', () => {
      const mockRequest = {
        method: 'DELETE',
        url: '/items/456'
      } as Request;

      expect(() => controller.handleNotFound(mockRequest)).toThrow(NotFoundException);
      expect(() => controller.handleNotFound(mockRequest)).toThrow(
        'No route found for DELETE /items/456'
      );
    });

    it('should throw 404 NotFoundException for PATCH request', () => {
      const mockRequest = {
        method: 'PATCH',
        url: '/data/update'
      } as Request;

      expect(() => controller.handleNotFound(mockRequest)).toThrow(NotFoundException);
      expect(() => controller.handleNotFound(mockRequest)).toThrow(
        'No route found for PATCH /data/update'
      );
    });

    it('should throw 404 NotFoundException for HEAD request', () => {
      const mockRequest = {
        method: 'HEAD',
        url: '/health/check'
      } as Request;

      expect(() => controller.handleNotFound(mockRequest)).toThrow(NotFoundException);
      expect(() => controller.handleNotFound(mockRequest)).toThrow(
        'No route found for HEAD /health/check'
      );
    });

    it.each([
      { method: 'GET', url: '/' },
      { method: 'GET', url: '/unknown' },
      { method: 'GET', url: '/api/v1/test' },
      { method: 'POST', url: '/webhook/notify' },
      { method: 'GET', url: '/deep/nested/path/with/many/segments' },
      { method: 'GET', url: '/path?query=value&other=param' },
      { method: 'GET', url: '/path#fragment' }
    ])('should handle various URL patterns', ({ method, url }) => {
      const mockRequest = { method, url } as Request;

      expect(() => controller.handleNotFound(mockRequest)).toThrow(NotFoundException);
      expect(() => controller.handleNotFound(mockRequest)).toThrow(
        `No route found for ${method} ${url}`
      );
    });

    it('should handle different HTTP methods uniformly', () => {
      const methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD'];
      const url = '/test/path';

      methods.forEach((method) => {
        const mockRequest = { method, url } as Request;

        expect(() => controller.handleNotFound(mockRequest)).toThrow(NotFoundException);
        expect(() => controller.handleNotFound(mockRequest)).toThrow(
          `No route found for ${method} ${url}`
        );
      });
    });

    it('should handle edge cases in URLs', () => {
      const edgeCases = [
        { method: 'GET', url: '' },
        { method: 'GET', url: '/a' },
        { method: 'GET', url: '/very-long-path-name-that-might-be-used-in-real-applications' },
        { method: 'GET', url: '/path/with/special-chars-!@#$%^&*()' },
        { method: 'GET', url: '/path/with/unicode/🚀/characters' }
      ];

      edgeCases.forEach(({ method, url }) => {
        const mockRequest = { method, url } as Request;

        expect(() => controller.handleNotFound(mockRequest)).toThrow(NotFoundException);
        expect(() => controller.handleNotFound(mockRequest)).toThrow(
          `No route found for ${method} ${url}`
        );
      });
    });
  });

  describe('controller route pattern', () => {
    it('should be configured as catch-all route with /* pattern', () => {
      const controllerInstance = new NotFoundController();
      expect(controllerInstance).toBeInstanceOf(NotFoundController);
    });
  });
});
