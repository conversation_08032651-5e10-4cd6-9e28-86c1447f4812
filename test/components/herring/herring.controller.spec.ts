import { Test, TestingModule } from '@nestjs/testing';
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { HerringController } from '../../../src/components/herring/herring.controller';
import { HerringService } from '../../../src/components/herring/herring.service';
import { LoggerService } from '../../../src/logger/logger.service';
import { VersionEnum } from '../../../src/types';

describe('HerringController', () => {
  let controller: HerringController;
  let herringService: HerringService;
  let loggerService: LoggerService;

  const mockHerringService = {
    get: vi.fn()
  };

  const mockLoggerService = {
    setContext: vi.fn(),
    log: vi.fn(),
    warn: vi.fn(),
    error: vi.fn()
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [HerringController],
      providers: [
        {
          provide: HerringService,
          useValue: mockHerringService
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService
        }
      ]
    }).compile();

    controller = module.get<HerringController>(HerringController);
    herringService = module.get<HerringService>(HerringService);
    loggerService = module.get<LoggerService>(LoggerService);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('GET /get_id', () => {
    const baseParams = {
      version: VersionEnum.v_1_0_0,
      timestamp: **********,
      ip: '127.0.0.1',
      useragent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      domain: 'example.com',
      serviceId: 'test-service',
      cms: 'test-cms',
      wid: 'test-wid',
      sid: 'test-sid',
      id5: 'ID5*test-id5' as const,
      uuid: 'test-uuid'
    };

    const mockHerringIds = {
      csid: '****************************************',
      cdid: '0987654321098765432109876543210987654321'
    };

    beforeEach(() => {
      mockHerringService.get.mockResolvedValue(mockHerringIds);
    });

    it('should return correct response for version 1.0.0', async () => {
      const params = { ...baseParams, version: VersionEnum.v_1_0_0 };

      const result = await controller.getId(params);

      expect(result).toEqual({
        request_version: VersionEnum.v_1_0_0,
        request_uuid: 'test-uuid',
        time_taken: expect.any(String),
        response_version: '1.0.0',
        tidcdmn: mockHerringIds.csid,
        tidcdvc: mockHerringIds.cdid
      });

      expect(mockLoggerService.log).toHaveBeenCalledWith('GET_ID_REQUEST_START', { params });
      expect(mockLoggerService.log).toHaveBeenCalledWith(
        'GET_ID_REQUEST_END',
        expect.objectContaining({
          response: result,
          timeTakenMs: expect.any(Number),
          herringId: mockHerringIds
        })
      );
    });

    it('should return correct response for version 1.0.1 and above', async () => {
      const params = { ...baseParams, version: VersionEnum.v_1_0_1 };

      const result = await controller.getId(params);

      expect(result).toEqual({
        request_version: VersionEnum.v_1_0_1,
        request_uuid: 'test-uuid',
        time_taken: expect.any(String),
        response_version: '1.0.1',
        tidcdmn: `sp${mockHerringIds.csid.padStart(20, '0')}`,
        tidcdvc: `he${mockHerringIds.cdid.padStart(20, '0')}`
      });
    });

    it('should handle uuid_val when uuid is not provided', async () => {
      const params = { ...baseParams, uuid: undefined, uuid_val: 'test-uuid-val' };

      const result = await controller.getId(params);

      expect(result.request_uuid).toBe('test-uuid-val');
    });

    it('should handle undefined uuid and uuid_val', async () => {
      const params = { ...baseParams, uuid: undefined, uuid_val: undefined };

      const result = await controller.getId(params);

      expect(result.request_uuid).toBeUndefined();
    });

    it('should log NO_CONSENT_FROM_USER when id5 is "0"', async () => {
      const params = { ...baseParams, id5: '0' as const };

      await controller.getId(params);

      expect(mockLoggerService.log).toHaveBeenCalledWith('NO_CONSENT_FROM_USER', {
        inputParams: params,
        message: 'ID=0. No consent from the user.'
      });
    });

    it('should calculate time_taken correctly', async () => {
      const startTime = Date.now();
      const result = await controller.getId(baseParams);
      const endTime = Date.now();

      const timeTaken = parseFloat(result.time_taken);
      expect(timeTaken).toBeGreaterThanOrEqual(0);
      expect(timeTaken).toBeLessThanOrEqual((endTime - startTime) / 1000 + 0.1); // Allow small margin
    });

    it('should pass correct parameters to herringService.get', async () => {
      const params = {
        ...baseParams,
        taid: 'test-taid',
        tpid: 'test-tpid',
        sid: 'test-sid',
        wid: 'test-wid'
      };

      await controller.getId(params);

      expect(mockHerringService.get).toHaveBeenCalledWith(
        params.useragent,
        params.taid,
        params.tpid,
        params.sid,
        params.id5,
        undefined,
        params.wid
      );
    });

    it('should handle different version enums correctly', async () => {
      const versions = [
        VersionEnum.v_1_0_0,
        VersionEnum.v_1_0_1,
        VersionEnum.v_1_1_0,
        VersionEnum.v_1_1_1,
        VersionEnum.v_1_1_2
      ];

      for (const version of versions) {
        const params = { ...baseParams, version };
        const result = await controller.getId(params);

        expect(result.request_version).toBe(version);
        expect(result.response_version).toBe(
          version >= VersionEnum.v_1_0_1 ? '1.0.1' : '1.0.0'
        );
      }
    });

    it('should pad csid and cdid for versions >= 1.0.1', async () => {
      const shortIds = {
        csid: '12345',
        cdid: '67890'
      };
      mockHerringService.get.mockResolvedValue(shortIds);

      const params = { ...baseParams, version: VersionEnum.v_1_0_1 };
      const result = await controller.getId(params);

      expect(result.tidcdmn).toBe(`sp${shortIds.csid.padStart(20, '0')}`);
      expect(result.tidcdvc).toBe(`he${shortIds.cdid.padStart(20, '0')}`);
    });
  });
});
