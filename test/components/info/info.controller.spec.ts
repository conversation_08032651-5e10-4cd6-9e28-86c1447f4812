import { Test, TestingModule } from '@nestjs/testing';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { InfoController } from '../../../src/components/info/info.controller';
import { InfoService } from '../../../src/components/info/info.service';
import { LoggerService } from '../../../src/logger/logger.service';

describe('InfoController', () => {
  let controller: InfoController;
  let infoService: InfoService;
  let loggerService: LoggerService;

  const mockInfoService = {
    appInfo: {
      maxCpu: 4,
      version: '1.0.0',
      nodeVersion: 'v18.17.0',
      environment: 'test',
      currentMachineTime: new Date('2024-01-01'),
      workerStartedTime: new Date('2024-01-01')
    },
    healthCheck: vi.fn().mockReturnValue('OK')
  };

  const mockLoggerService = {
    error: vi.fn()
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [InfoController],
      providers: [
        {
          provide: InfoService,
          useValue: mockInfoService
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService
        }
      ]
    }).compile();

    controller = module.get<InfoController>(InfoController);
    infoService = module.get<InfoService>(InfoService);
    loggerService = module.get<LoggerService>(LoggerService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('GET /info/app', () => {
    it('should return app info', () => {
      const result = controller.appInfo();

      expect(result).toEqual({
        maxCpu: 4,
        version: '1.0.0',
        nodeVersion: 'v18.17.0',
        environment: 'test',
        currentMachineTime: expect.any(Date),
        workerStartedTime: expect.any(Date)
      });
    });
  });

  describe('GET /info/check', () => {
    it('should return health check status', () => {
      const result = controller.appCheck();

      expect(result).toBe('OK');
      expect(mockInfoService.healthCheck).toHaveBeenCalled();
    });
  });
});
