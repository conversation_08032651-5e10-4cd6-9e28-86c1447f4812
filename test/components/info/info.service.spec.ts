import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import { InfoService } from '../../../src/components/info/info.service';
const cluster = require('cluster');

const { env } = process;

describe('InfoService test suite', () => {
  beforeEach(() => {
    vi.resetModules();
    process.env = { ...env };
  });

  afterEach(() => {
    process.env = env;
  });

  const infoService = new InfoService();
  test('infoService should have correct property types', () => {
    expect(infoService).toMatchObject({
      cpuQuantity: expect.any(Number),
      nodeVersion: expect.any(String),
      appVersion: expect.any(String),
      currentMachineTime: expect.any(Date),
      workerStartedTime: expect.any(Date),
      healthCheck: expect.any(Function)
    });
  });

  test('infoService.healthCheck; should return string OK; isWorker=true', () => {
    cluster.isWorker = true;
    expect(infoService.healthCheck()).toEqual('OK');
  });

  test('infoService.healthCheck; should throw an exception; liveWorkers=0; needToBe=0; isMaster=true', () => {
    cluster.isWorker = false;
    cluster.isPrimary = true;
    cluster.workers = {};
    expect(() => {
      infoService.healthCheck();
    }).toThrowError('HEALTH_CHECK_ERROR');
  });

  test('infoService.healthCheck; should return result; liveWorkers=2; needToBe=1; isMaster=true', () => {
    const clusterWorkers = [
      {
        process: {
          pid: '890022'
        },
        id: '55446655',
        state: 'listening'
      },
      {
        process: {
          pid: '890023'
        },
        id: '55446656',
        state: 'listening'
      }
    ];

    const healthCheckResponse = [
      {
        cluster: '55446655',
        state: 'listening'
      },
      {
        cluster: '55446656',
        state: 'listening'
      }
    ];

    cluster.isPrimary = true;
    cluster.workers = clusterWorkers;
    expect(infoService.healthCheck()).toEqual(JSON.stringify(healthCheckResponse));
  });
});
