import { Test, TestingModule } from '@nestjs/testing';
import { describe, it, expect, beforeEach } from 'vitest';
import { K8sHealthController } from '../../../src/components/k8s-health/k8s-health.controller';

describe('K8sHealthController', () => {
  let controller: K8sHealthController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [K8sHealthController]
    }).compile();

    controller = module.get<K8sHealthController>(K8sHealthController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should return empty object', () => {
    const response = controller.healthCheck();

    expect(response).toEqual({});
  });
});
