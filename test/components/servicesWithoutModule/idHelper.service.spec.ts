import { Test, TestingModule } from '@nestjs/testing';
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { IdHelperService } from '../../../src/components/servicesWIthoutModule/idHelper.service';
import { LoggerService } from '../../../src/logger/logger.service';
import { RedisService } from '../../../src/redis/redis.service';
import { Herring<PERSON>ey } from '../../../src/types';

describe('IdHelperService', () => {
  let service: IdHelperService;
  let redisService: RedisService;
  let loggerService: LoggerService;

  const mockRedisService = {
    get: vi.fn(),
    set: vi.fn()
  };

  const mockLoggerService = {
    setContext: vi.fn(),
    log: vi.fn(),
    warn: vi.fn(),
    error: vi.fn()
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        IdHelperService,
        {
          provide: RedisService,
          useValue: mockRedisService
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService
        }
      ]
    }).compile();

    service = module.get<IdHelperService>(IdHelperService);
    redisService = module.get<RedisService>(RedisService);
    loggerService = module.get<LoggerService>(LoggerService);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateNewRandomId', () => {
    it('should generate a random ID using crypto.getRandomValues', () => {
      const result = service.generateNewRandomId();

      expect(result).toEqual(expect.any(String));
      expect(result).toMatch(/^\d+$/);
    });

    it('should generate different IDs on subsequent calls', () => {
      // make array of 100 values and populate them using generateNewRandomId
      const values = Array.from({ length: 100 }, () => service.generateNewRandomId());

      // check if all values are unique
      expect(new Set(values).size).toBe(values.length);
    });

    it('should log the generated ID', () => {
      const result = service.generateNewRandomId();

      expect(mockLoggerService.log).toHaveBeenCalledWith('GENERATE_NEW_RANDOM_ID', {
        id: expect.any(String)
      });
    });
  });

  describe('getOrCreateHerringID', () => {
    const testKey: HerringKey = 'ind:test-identity';
    const testInputs = { testField: 'testValue' };

    describe('when ID exists in Redis', () => {
      const existingId = 'existing-herring-id';

      beforeEach(() => {
        mockRedisService.get.mockResolvedValue(existingId);
      });

      it('should return existing ID from Redis', async () => {
        const result = await service.getOrCreateHerringID(testKey, testInputs);

        expect(result).toBe(existingId);
        expect(mockRedisService.get).toHaveBeenCalledWith(testKey);
        expect(mockRedisService.set).not.toHaveBeenCalled();
      });

      it('should log GET_HERRING_ID event when retrieving existing ID', async () => {
        await service.getOrCreateHerringID(testKey, testInputs);

        expect(mockLoggerService.log).toHaveBeenCalledWith('GET_HERRING_ID', {
          inputs: testInputs,
          dbKey: testKey,
          dbValue: existingId
        });
      });
    });

    describe('when ID does not exist in Redis', () => {
      const newGeneratedId = 'new-generated-id';

      beforeEach(() => {
        mockRedisService.get.mockResolvedValue(null);
        vi.spyOn(service, 'generateNewRandomId').mockReturnValue(newGeneratedId);
      });

      afterEach(() => {
        vi.restoreAllMocks();
      });

      it('should generate new ID and store it in Redis', async () => {
        const result = await service.getOrCreateHerringID(testKey, testInputs);

        expect(result).toBe(newGeneratedId);
        expect(mockRedisService.get).toHaveBeenCalledWith(testKey);
        expect(service.generateNewRandomId).toHaveBeenCalled();
        expect(mockRedisService.set).toHaveBeenCalledWith(testKey, newGeneratedId);
      });

      it('should log CREATED_HERRING_ID event when creating new ID', async () => {
        await service.getOrCreateHerringID(testKey, testInputs);

        expect(mockLoggerService.log).toHaveBeenCalledWith('CREATED_HERRING_ID', {
          inputs: testInputs,
          dbKey: testKey,
          dbValue: newGeneratedId
        });
      });
    });
  });
});
