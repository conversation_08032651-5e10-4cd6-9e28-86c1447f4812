import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { RedisController } from '../../src/redis/redis.controller';
import { RedisService } from '../../src/redis/redis.service';

describe('RedisController', () => {
  let redisController: RedisController;
  let redisService: RedisService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RedisController],
      providers: [
        {
          provide: RedisService,
          useValue: {
            get: vi.fn()
          }
        }
      ]
    }).compile();

    redisController = module.get<RedisController>(RedisController);
    redisService = module.get<RedisService>(RedisService);
  });

  it('should return the value for an existing key', async () => {
    vi.spyOn(redisService, 'get').mockResolvedValue('test-value');

    const result = await redisController.getKey({ key: 'test-key' });
    expect(result).toBe('test-value');
    expect(redisService.get).toHaveBeenCalledWith('test-key');
  });

  it('should throw NotFoundException for a non-existing key', async () => {
    vi.spyOn(redisService, 'get').mockResolvedValue(null);

    await expect(redisController.getKey({ key: 'non-existing-key' })).rejects.toThrow(
      NotFoundException
    );
    expect(redisService.get).toHaveBeenCalledWith('non-existing-key');
  });
});
