import { describe, test, expect } from 'vitest';
import { xmlParser } from '../../src/utils/xmlParser';

describe('xmlParser test suite', () => {
  const emptyVastJson = {
    VAST: {
      _attributes: {
        version: '4.0'
      },
      Ad: []
    }
  };

  const emptyVastJson1 = {
    VAST: {
      _attributes: {
        version: '4.0'
      }
    }
  };

  const emptyVastJsonEncoded = '%3CVAST%20version%3D%224.0%22%3E%3C%2FVAST%3E';

  const emptyVastResponse = '<VAST version="4.0"></VAST>';

  const emptyVastEncoded = encodeURIComponent(emptyVastResponse);

  const logger = () => {
    /* silence is golden */
  };

  test('fromXMLtoJSON is a function', () => {
    expect(typeof xmlParser.fromXMLtoJSON).toEqual('function');
  });

  test('fromXMLtoJSON accepts null input', () => {
    expect(xmlParser.fromXMLtoJSON(null)).toEqual(null);
  });

  test('fromXMLtoJSON convert empty vast', () => {
    expect(xmlParser.fromXMLtoJSON(emptyVastResponse)).toEqual(emptyVastJson1);
  });

  test('fromEncodedToJSON is a function', () => {
    expect(typeof xmlParser.fromEncodedToJSON).toEqual('function');
  });

  test('fromEncodedToJSON accepts empty string', () => {
    expect(xmlParser.fromEncodedToJSON('')).toEqual(null);
  });

  test('fromEncodedToJSON malformed input', () => {
    expect(xmlParser.fromEncodedToJSON('incorrect xml')).toBeNull();
  });

  test('fromEncodedToJSON convert empty vast', () => {
    expect(xmlParser.fromEncodedToJSON(emptyVastEncoded)).toEqual(emptyVastJson1);
  });

  test('fromJSONtoXML is a function', () => {
    expect(typeof xmlParser.fromJSONtoXML).toEqual('function');
  });

  test('fromJSONtoXML accepts null input', () => {
    expect(xmlParser.fromJSONtoXML(null)).toEqual('');
  });

  test('fromJSONtoXML convert empty vast', () => {
    expect(xmlParser.fromJSONtoXML(emptyVastJson)).toEqual(emptyVastResponse);
  });

  test('fromJSONtoXML convert empty vast, decode response', () => {
    expect(xmlParser.fromJSONtoXML(emptyVastJson, true)).toEqual(emptyVastResponse);
  });

  test('fromJSONtoEncoded convert empty vast', () => {
    expect(xmlParser.fromJSONtoEncoded(emptyVastJson)).toEqual(emptyVastJsonEncoded);
  });

  test('fromJSONtoEncoded empty string', () => {
    expect(xmlParser.fromJSONtoEncoded(null)).toEqual('');
  });

  test('fromEncodedToDecoded convert empty vast', () => {
    expect(xmlParser.fromEncodedToDecoded(emptyVastJsonEncoded)).toEqual(emptyVastResponse);
  });

  test('fromDecodedToEncoded convert empty vast', () => {
    expect(xmlParser.fromDecodedToEncoded(emptyVastResponse)).toEqual(emptyVastJsonEncoded);
  });
});
