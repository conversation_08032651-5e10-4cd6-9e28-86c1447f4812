import { defineConfig, ViteUserConfig } from 'vitest/config';
import swc from 'unplugin-swc';
const config: ViteUserConfig = {
  test: {
    globals: true,
    environment: 'node',
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      include: ['test/**/*.ts'],
      exclude: ['node_modules', 'dist']
    },
    include: ['test/**/*.spec.ts'],
    testTimeout: 10000,
    hookTimeout: 10000,
    typecheck: {
      enabled: true,
      tsconfig: 'tsconfig.json',
      include: ['test/**/*.spec.ts']
    }
  },
  plugins: [
    swc.vite({
      module: { type: 'es6' }
    })
  ]
};

export default defineConfig(config);
